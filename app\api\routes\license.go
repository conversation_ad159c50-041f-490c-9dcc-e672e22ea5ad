package routes

import (
	"crypto/hmac"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"

	"github.com/gin-gonic/gin"
	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/domains/license"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/localizer"
	"github.com/onwa/pkg/middleware"
	"github.com/onwa/pkg/onwalog"
	"github.com/onwa/pkg/state"
)

func LicenseRoutes(r *gin.RouterGroup, s license.Service) {
	r.POST("/license", middleware.FromClient(), middleware.Authorized(), addLicense(s))
	r.GET("/license", middleware.FromClient(), middleware.Authorized(), currentLicense(s))

	r.GET("/notification", middleware.FromClient(), middleware.Authorized(), getNotifInformation(s))
	r.POST("/notification", middleware.FromClient(), middleware.Authorized(), updateNotifInformation(s))
	r.POST("/notification/after/offline", middleware.FromClient(), middleware.Authorized(), updateNotifAfterOffline(s))

	r.POST("/create-invoice", middleware.FromClient(), middleware.Authorized(), createInvoice(s))

	r.POST("/webhook", webhook(s))
	r.POST("/webhook/crypto", webhookForCrypto(s))
}

// @Summary Add License
// @Description Add License
// @Tags License Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdateLicense true "request Lisence Add"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /license [POST]
func addLicense(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdateLicense
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		err := s.AddLicense(c, req)
		if err != nil {
			if err.Error() == "license_error_only_3_ads" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("license_error_only_3_ads", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			} else if err.Error() == "license_error_comment_already_done" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("license_error_comment_already_done", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			} else if err.Error() == "license_error_type_must_be_free" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("license_type_must_be_free", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			} else {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("update_error", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			}
		}

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("update_success", state.CurrentPhoneLang(c), nil),
			"status": 201,
		})
	}
}

// @Summary Get Current License
// @Description Get Current License
// @Tags License Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /license [GET]
func currentLicense(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetLastLicense(c)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "currentLicense",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}
		onwalog.CreateLog(&entities.Log{
			Title:     "currentLicense",
			Message:   "Success: current license fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func webhook(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var webhook_payload entities.Webhook
		if err := c.ShouldBindJSON(&webhook_payload); err != nil {
			c.AbortWithStatusJSON(400, gin.H{"error": err.Error()})
			return
		}

		if err := s.Webhook(c, webhook_payload); err != nil {
			c.AbortWithStatusJSON(400, gin.H{"error": err.Error()})
			return
		}

		c.JSON(200, gin.H{"message": "success"})
	}
}

// @Summary Get Notif Info
// @Description Get Notif Info
// @Tags License Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /notification [GET]
func getNotifInformation(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetNotifInformation(c)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "getNotifInformation",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}
		onwalog.CreateLog(&entities.Log{
			Title:     "getNotifInformation",
			Message:   "Success: notif information fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Update Notif Info
// @Description Update Notif Info
// @Tags License Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	data	query	string	false	"Data"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /notification [POST]
func updateNotifInformation(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		data := c.Query("data")
		err := s.UpdateNotifInformation(c, data)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "updateNotifInformation",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("update_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}
		onwalog.CreateLog(&entities.Log{
			Title:     "updateNotifInformation",
			Message:   "Success: notif information updated (" + data + ")",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})
		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("update_success", state.CurrentPhoneLang(c), nil),
			"status": 201,
		})
	}
}

// @Summary Update Notif After Offline
// @Description Update Notif After Offline
// @Tags License Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	data	query	string	false	"Data"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /notification/after/offline [POST]
func updateNotifAfterOffline(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		data := c.Query("data")
		err := s.UpdateNotifAfterOffline(c, data)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "updateNotifAfterOffline",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("update_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}
		onwalog.CreateLog(&entities.Log{
			Title:     "updateNotifAfterOffline",
			Message:   "Success: notif after offline updated (" + data + ")",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})
		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("update_success", state.CurrentPhoneLang(c), nil),
			"status": 201,
		})
	}
}

// @Summary Create Invoice For Crypto Payment
// @Description Create Invoice For Crypto Payment
// @Tags License Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreateInvoice true "request Create Invoice"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /create-invoice [POST]
func createInvoice(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateInvoice
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		invoice_url, err := s.CreateInvoice(c, req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("create_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   invoice_url,
			"status": 201,
		})
	}
}

func webhookForCrypto(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var webhookPayload map[string]interface{}
		if err := c.ShouldBindJSON(&webhookPayload); err != nil {
			c.AbortWithStatusJSON(400, gin.H{"error": err.Error()})
			return
		}

		// Read signature from header
		signatureHeader := c.GetHeader("x-nowpayments-sig")
		if signatureHeader == "" {
			c.AbortWithStatusJSON(400, gin.H{"error": "Missing signature header"})
			return
		}

		// Sort the keys
		keys := make([]string, 0, len(webhookPayload))
		for k := range webhookPayload {
			keys = append(keys, k)
		}
		sort.Strings(keys)

		sortedPayload := make(map[string]interface{})
		for _, k := range keys {
			sortedPayload[k] = webhookPayload[k]
		}
		jsonBytes, err := json.Marshal(sortedPayload)
		if err != nil {
			c.AbortWithStatusJSON(500, gin.H{"error": "Failed to serialize payload"})
			return
		}

		h := hmac.New(sha512.New, []byte(config.InitConfig().NowPayments.IPNKey))
		h.Write(jsonBytes)
		calculatedSig := hex.EncodeToString(h.Sum(nil))

		// Compare signatures
		if !hmac.Equal([]byte(calculatedSig), []byte(signatureHeader)) {
			c.AbortWithStatusJSON(401, gin.H{"error": "Invalid signature"})
			return
		}

		var (
			invoice_id, order_id, payment_status string
		)

		if v, ok := webhookPayload["invoice_id"]; ok {
			switch val := v.(type) {
			case float64:
				invoice_id = fmt.Sprintf("%.0f", val)
			case string:
				invoice_id = val
			default:
				invoice_id = ""
			}
		}

		if v, ok := webhookPayload["order_id"].(string); ok {
			order_id = v
		}

		if v, ok := webhookPayload["payment_status"].(string); ok {
			payment_status = v
		}

		if err := s.WebhookForCrypto(c, invoice_id, order_id, payment_status); err != nil {
			c.AbortWithStatusJSON(400, gin.H{"error": err.Error()})
			return
		}

		c.JSON(200, gin.H{"message": "success"})
	}
}
