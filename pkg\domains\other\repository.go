package other

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/job"
	"github.com/onwa/pkg/localizer"
	"github.com/onwa/pkg/state"
	"github.com/onwa/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	contact(ctx context.Context, req dtos.RequestForContact, file_names []string) error

	addSSS(ctx context.Context, req dtos.RequestForSSS) error
	getSSS(ctx context.Context) ([]entities.SSS, error)

	sendNotification(ctx context.Context, req dtos.RequestForSendNotification) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

const message_body = "Sender-email: %s\nSender-phone: %s\nSubject: %s\nMessage: %s\nFile Links: %v\n\nUser Informationn\nUser ID: %s\nDevice ID: %s\nReg ID: %s\nPhone-language: %s\nLast Version Name: %s\n"

func (r *repository) contact(ctx context.Context, req dtos.RequestForContact, file_names []string) error {
	var (
		current_user entities.User
		body         string
		contact      entities.Contact
	)

	if err := r.db.Model(&entities.User{}).
		Where("id = ?", state.GetCurrentUserID(ctx)).
		First(&current_user).Error; err != nil {
		return err
	}

	body = fmt.Sprintf(message_body, req.Email, req.Phone, req.Subject, req.Message, file_names, current_user.ID.String(), current_user.DeviceID, current_user.RegID, current_user.PhoneLanguage, current_user.LastVersionName)

	contact.SendDate = time.Now()
	contact.Email = req.Email
	contact.Phone = req.Phone
	contact.Subject = req.Subject
	contact.Message = req.Message
	contact.UserID = current_user.ID

	var last_s string

	for _, v := range file_names {
		last_s += v + "\n"
	}

	contact.AssetURL = last_s

	if err := r.db.WithContext(ctx).Model(&entities.Contact{}).Create(&contact).Error; err != nil {
		return err
	}

	job.SendMail(
		config.InitConfig().App.AdminEmail,
		"Contact US",
		body,
	)

	return nil
}

func (r *repository) addSSS(ctx context.Context, req dtos.RequestForSSS) error {
	var sss entities.SSS

	sss.Title = req.Title
	sss.Question = req.Question
	sss.Answer = req.Answer

	return r.db.WithContext(ctx).Model(&entities.SSS{}).Create(&sss).Error
}

func (r *repository) getSSS(ctx context.Context) ([]entities.SSS, error) {
	var sss []entities.SSS
	err := r.db.WithContext(ctx).
		Model(&entities.SSS{}).
		Order("created_at desc").
		Find(&sss).Error
	return sss, err
}

func (r *repository) sendNotification(ctx context.Context, req dtos.RequestForSendNotification) error {
	var (
		current_user     entities.User
		current_presence entities.Presence
	)

	r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("session_id = ?", req.SessionID).
		First(&current_user)

	if current_user.ID == uuid.Nil {
		return errors.New("user not found")
	}

	r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Where("session_id = ? AND phone = ?", req.SessionID, req.PhoneNumber).
		First(&current_presence)

	if current_presence.ID == uuid.Nil {
		return errors.New("presence not found")
	}

	if current_presence.PushNotifToken == "" {
		return errors.New("push notif token not found")
	}

	data := dtos.NotificationData{
		IncludeSubscriptionIDS: []string{current_presence.PushNotifToken},
		Headings: map[string]string{
			"en": current_presence.ContactName,
		},
		Contents: map[string]string{
			"en": localizer.GetTranslated("notification_happen", current_presence.Local, nil),
		},
	}

	if err := utils.PushNotifRequest(&data); err != nil {
		return err
	}

	return nil
}
