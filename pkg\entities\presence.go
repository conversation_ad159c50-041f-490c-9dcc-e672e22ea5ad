package entities

import (
	"time"

	"github.com/google/uuid"
)

type Presence struct {
	Base
	SessionID                uuid.UUID `json:"session_id"`
	UserID                   uuid.UUID `json:"user_id" gorm:"index"`
	DeviceID                 string    `json:"device_id"`
	Phone                    string    `json:"phone"`
	StartedAt                time.Time `json:"started_at"`
	EndedAt                  time.Time `json:"ended_at"`
	TimeZone                 string    `json:"time_zone"`
	NotificationInterval     int       `json:"notification_interval"`
	NotificationAfterOffline bool      `json:"notification_after_offline" default:"false"`
	PushNotifToken           string    `json:"push_notif_token"`
	ContactID                string    `json:"contact_id"`
	ContactName              string    `json:"contact_name"`
	Done                     bool      `json:"done" gorm:"default:false"`
	Local                    string    `json:"local" default:"en-US"`
}
