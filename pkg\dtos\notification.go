package dtos

type NotificationData struct {
	AppID                  string            `json:"app_id"`
	IncludeSubscriptionIDS []string          `json:"include_subscription_ids"`
	Headings               map[string]string `json:"headings"`
	Contents               map[string]string `json:"contents"`
}

type RequestForSendNotification struct {
	SessionID   string `json:"session_id"`
	PhoneNumber string `json:"phone_number"`
}
