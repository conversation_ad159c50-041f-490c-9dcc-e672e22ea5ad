package wp

import (
	"context"
	"errors"

	"github.com/google/uuid"
	customerrors "github.com/onwa/pkg/customErrors"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/state"
)

type Service interface {
	SessionCreate(ctx context.Context, req dtos.RequestForCreateSession) (dtos.ResponseForSessionCreate, error)
	SessionLogout(ctx context.Context) error

	RequestWPCode(ctx context.Context, req dtos.RequestForWPCode) (string, error)
	CheckDeviceWithSession(ctx context.Context) (dtos.ResponseForCheckDeviceWithSession, error)
	CheckDeviceWithSessionWithoutTimeout(ctx context.Context) (dtos.ResponseForCheckDeviceWithSession, error)

	GetProfilePhoto(ctx context.Context, req dtos.RequestForProfilePhoto) (dtos.CoreResponseForProfilePhone, error)

	PhoneNumberGetAll(ctx context.Context) ([]entities.Number, error)
	PhoneNumberCreate(ctx context.Context, req dtos.RequestForWPPhoneNumbers) ([]entities.Number, error)
	PhoneNumberDelete(ctx context.Context, id uuid.UUID) error

	PresenceStart(ctx context.Context, req dtos.RequestForPresenceStart) (dtos.ResponseForPresenceStart, error)
	PresenceStop(ctx context.Context, presence_id string) error
	PresenceGetLast(ctx context.Context, presence_id string) (dtos.CoreResponseForPresenceLast, error)
	PresenceGetAll(ctx context.Context, status, phone string) ([]entities.Presence, error)
	PresenceGetByID(ctx context.Context, presence_id string) (entities.Presence, error)

	ReportGet(ctx context.Context, page, perPage int, time_range, start_date, end_date, presence_id string) (*dtos.CoreResponseForPresenceHistory, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) SessionCreate(ctx context.Context, req dtos.RequestForCreateSession) (dtos.ResponseForSessionCreate, error) {
	return s.repository.sessionCreate(ctx, req)
}

func (s *service) SessionLogout(ctx context.Context) error {
	return s.repository.sessionStop(ctx)
}

func (s *service) RequestWPCode(ctx context.Context, req dtos.RequestForWPCode) (string, error) {
	return s.repository.requestWpCode(ctx, req)
}

func (s *service) CheckDeviceWithSession(ctx context.Context) (dtos.ResponseForCheckDeviceWithSession, error) {
	return s.repository.checkDeviceWithSession(ctx)
}

func (s *service) CheckDeviceWithSessionWithoutTimeout(ctx context.Context) (dtos.ResponseForCheckDeviceWithSession, error) {
	return s.repository.checkDeviceWithSessionWithoutTimeout(ctx)
}

func (s *service) GetProfilePhoto(ctx context.Context, req dtos.RequestForProfilePhoto) (dtos.CoreResponseForProfilePhone, error) {
	return s.repository.getProfilePhoto(ctx, req.Phone, req.ForSession)
}

func (s *service) PhoneNumberGetAll(ctx context.Context) ([]entities.Number, error) {
	var numbers []entities.Number
	err := s.repository.phoneNumberGetAll(ctx, &numbers)
	return numbers, err
}

func (s *service) PhoneNumberCreate(ctx context.Context, req dtos.RequestForWPPhoneNumbers) ([]entities.Number, error) {
	var (
		numbers   []entities.Number
		user_uuid = state.GetCurrentUserID(ctx)
	)

	count, err := s.repository.phoneNumberCountForUser(ctx, user_uuid)
	if err != nil {
		return nil, err
	}

	if count >= 3 {
		return nil, errors.New(customerrors.ErrPhoneNumberMax)
	}

	for _, v := range req.PhoneNumbers {
		var number entities.Number
		if v.Name == "" {
			number.Name = v.RawPhoneNumber
		} else {
			number.Name = v.Name
		}
		number.DialCode = v.DialCode
		number.PhoneNumber = v.PhoneNumber
		number.RawPhoneNumber = v.RawPhoneNumber
		number.UserID = user_uuid
		number.DeviceID = state.GetCurrentDeviceID(ctx)

		numbers = append(numbers, number)
	}
	err = s.repository.phoneNumberCreate(ctx, &numbers)
	return numbers, err
}

func (s *service) PhoneNumberDelete(ctx context.Context, id uuid.UUID) error {
	return s.repository.phoneNumberDelete(ctx, id)
}

func (s *service) PresenceGetLast(ctx context.Context, presence_id string) (dtos.CoreResponseForPresenceLast, error) {
	return s.repository.presenceGetLast(ctx, presence_id)
}

func (s *service) PresenceStart(ctx context.Context, req dtos.RequestForPresenceStart) (dtos.ResponseForPresenceStart, error) {
	return s.repository.presenceStart(ctx, req)
}

func (s *service) PresenceStop(ctx context.Context, presence_id string) error {
	return s.repository.presenceStop(ctx, presence_id)
}

func (s *service) PresenceGetAll(ctx context.Context, status, phone string) ([]entities.Presence, error) {
	var presences []entities.Presence
	err := s.repository.presenceGetAll(ctx, status, phone, &presences)
	return presences, err
}

func (s *service) PresenceGetByID(ctx context.Context, presence_id string) (entities.Presence, error) {
	var presences entities.Presence
	err := s.repository.presenceGetByID(ctx, presence_id, &presences)
	return presences, err
}

func (s *service) ReportGet(ctx context.Context, page, perPage int, time_range, start_date, end_date, presence_id string) (*dtos.CoreResponseForPresenceHistory, error) {
	return s.repository.reportGet(ctx, page, perPage, time_range, start_date, end_date, presence_id)
}
